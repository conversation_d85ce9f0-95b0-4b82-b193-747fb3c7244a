package com.lfb.android.footprint

import android.os.Bundle
import androidx.activity.compose.setContent
import com.lfb.android.footprint.ui.components.locationModeScreen.LocationModeScreen
import com.lfb.android.footprint.ui.theme.ThemeUtils

class LocationModeActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // 根据用户的地图主题配置设置Activity主题
        setTheme(ThemeUtils.getActivityThemeForMapDisplayType(this))

        super.onCreate(savedInstanceState)

        setContent {
            LocationModeScreen(
                onBackClick = { finish() }
            )
        }
    }
}
